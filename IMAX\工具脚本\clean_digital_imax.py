#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def clean_number(value):
    """清理数字格式，移除不必要的.0后缀"""
    if value == '--':
        return value
    
    try:
        # 尝试转换为浮点数
        num = float(value)
        # 如果是整数，返回整数格式
        if num.is_integer():
            return str(int(num))
        else:
            return str(num)
    except (ValueError, TypeError):
        return value

def main():
    print("开始清理数字IMAX JSON数据...")
    
    try:
        # 读取JSON文件
        with open('../数字IMAX.json', 'r', encoding='utf-8') as f:
            cinemas = json.load(f)
        
        print(f"读取到 {len(cinemas)} 家影院数据")
        
        # 清理数据
        for cinema in cinemas:
            cinema['screenArea'] = clean_number(cinema['screenArea'])
            cinema['screenWidth'] = clean_number(cinema['screenWidth'])
            cinema['screenHeight'] = clean_number(cinema['screenHeight'])
            cinema['seatCount'] = clean_number(cinema['seatCount'])
        
        # 保存清理后的数据
        with open('../数字IMAX.json', 'w', encoding='utf-8') as f:
            json.dump(cinemas, f, ensure_ascii=False, indent=2)
        
        print("✅ 数据清理完成")
        
        # 显示前5个示例
        print("\n前5个影院示例:")
        for idx, cinema in enumerate(cinemas[:5], 1):
            print(f"{idx}. {cinema['name']}")
            print(f"   类型: {cinema['projectorType']}")
            print(f"   银幕: {cinema['screenWidth']}m × {cinema['screenHeight']}m ({cinema['screenArea']}㎡)")
            print(f"   座位: {cinema['seatCount']}")
            print(f"   音响: {cinema['audioSystem'] or '无'}")
            print()
        
        # 生成最终统计信息
        stats = {
            'total': len(cinemas),
            'with_12_channel': len([c for c in cinemas if '12声道' in c['audioSystem']]),
            'without_screen_data': len([c for c in cinemas if c['screenArea'] == '--']),
            'without_seat_data': len([c for c in cinemas if c['seatCount'] == '--']),
            'complete_data': len([c for c in cinemas if c['screenArea'] != '--' and c['screenWidth'] != '--' and c['screenHeight'] != '--' and c['seatCount'] != '--'])
        }
        
        print("📊 最终统计信息:")
        print(f"总数: {stats['total']}")
        print(f"12声道音响: {stats['with_12_channel']}")
        print(f"缺少银幕数据: {stats['without_screen_data']}")
        print(f"缺少座位数据: {stats['without_seat_data']}")
        print(f"数据完整: {stats['complete_data']}")
        
        print("\n🎉 数字IMAX数据转换完成！")
        print("📁 输出文件: 数字IMAX.json")
        
    except Exception as e:
        print(f"❌ 清理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
