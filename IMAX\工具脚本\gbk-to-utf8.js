const fs = require('fs');

// 读取文件为二进制
const buffer = fs.readFileSync('../数字IMAX.csv');

// GBK 常用字符映射表（扩展版）
const gbkToUtf8Map = {
    // 基本字符
    'Ӱ': '影', '��': '城', '��': '名', '��': '称',
    '��': '放', '��': '映', '��': '机', '��': '型', '��': '号',
    '��': '声', '��': '道', '��': '音', '��': '响', '��': '系', '��': '统',
    '��': '银', '��': '幕', '��': '面', '��': '积',
    '��': '平', '��': '方', '��': '米',
    '��': '宽', '��': '度', '��': '高', '��': '座', '��': '位', '��': '数',
    
    // 地名
    '�Ϻ�': '上海', '����': '万达', '����': '北京', '����': '深圳', 
    '����': '广州', '����': '杭州', '����': '南京', '����': '苏州',
    '����': '无锡', '����': '成都', '����': '重庆', '����': '武汉',
    '����': '长沙', '����': '天津', '����': '沈阳', '����': '大连',
    '����': '哈尔滨', '����': '昆明', '����': '西安', '����': '郑州',
    '����': '太原', '����': '兰州', '����': '福州', '����': '厦门',
    '����': '贵阳', '����': '南宁', '����': '海口', '����': '石家庄',
    '����': '济南', '����': '合肥', '����': '南昌', '����': '长春',
    
    // 影院相关
    '��': '店', '��': '广', '��': '场', '��': '中', '��': '心',
    '��': '国', '��': '际', '��': '商', '��': '业', '��': '购', '��': '物',
    '��': '大', '��': '厦', '��': '世', '��': '界', '��': '环', '��': '球',
    '��': '金', '��': '融', '��': '财', '��': '富', '��': '时', '��': '代',
    '��': '新', '��': '天', '��': '地', '��': '华', '��': '润', '��': '恒',
    '��': '隆', '��': '万', '��': '科', '��': '保', '��': '利', '��': '嘉',
    '��': '德', '��': '百', '��': '联', '��': '友', '��': '谊', '��': '第',
    '��': '一', '��': '二', '��': '三', '��': '四', '��': '五', '��': '六',
    '��': '七', '��': '八', '��': '九', '��': '十',
    
    // 其他常用字
    '��': '年', '��': '月', '��': '日', '��': '开', '��': '业',
    '��': '激', '��': '光', '��': '氙', '��': '灯', '��': '数', '��': '字',
    '��': '科', '��': '技', '��': '博', '��': '物', '��': '馆',
    '��': '电', '��': '影', '��': '院', '��': '厅', '��': '室',
    '��': '香', '��': '港', '��': '澳', '��': '门', '��': '台', '��': '湾',
    '��': '省', '��': '市', '��': '区', '��': '县', '��': '镇', '��': '街',
    '��': '路', '��': '道', '��': '巷', '��': '弄', '��': '号', '��': '楼',
    '��': '层', '��': '单', '��': '元', '��': '室', '��': '户',
    
    // 标点和符号
    '��': '（', '��': '）', '��': '【', '��': '】', '��': '《', '��': '》',
    '��': '"', '��': '"', '��': "'", '��': "'", '��': '、', '��': '。',
    '��': '，', '��': '；', '��': '：', '��': '？', '��': '！',
    
    // 数字相关
    '��': '零', '��': '一', '��': '二', '��': '三', '��': '四', '��': '五',
    '��': '六', '��': '七', '��': '八', '��': '九', '��': '十', '��': '百',
    '��': '千', '��': '万', '��': '亿'
};

// 转换函数
function convertGbkToUtf8(text) {
    let result = text;
    
    // 应用映射表
    for (const [gbk, utf8] of Object.entries(gbkToUtf8Map)) {
        result = result.replace(new RegExp(gbk.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), utf8);
    }
    
    return result;
}

try {
    // 读取为 latin1 编码（保持字节不变）
    const latin1Content = buffer.toString('latin1');
    console.log('原始内容前100字符:', latin1Content.substring(0, 100));
    
    // 转换编码
    const convertedContent = convertGbkToUtf8(latin1Content);
    console.log('转换后前100字符:', convertedContent.substring(0, 100));
    
    // 保存为 UTF-8
    fs.writeFileSync('../数字IMAX_utf8.csv', convertedContent, 'utf8');
    console.log('\n✅ 已保存为 UTF-8 编码: 数字IMAX_utf8.csv');
    
    // 显示前几行
    const lines = convertedContent.split('\n').slice(0, 10);
    console.log('\n前10行内容:');
    lines.forEach((line, index) => {
        if (line.trim()) {
            console.log(`${index + 1}: ${line}`);
        }
    });
    
    // 统计信息
    const totalLines = convertedContent.split('\n').filter(line => line.trim()).length;
    console.log(`\n📊 总行数: ${totalLines}`);
    
    // 检查转换质量
    const hasChineseChars = /[\u4e00-\u9fff]/.test(convertedContent);
    const hasImaxKeyword = convertedContent.includes('IMAX');
    
    console.log(`包含中文字符: ${hasChineseChars ? '✅' : '❌'}`);
    console.log(`包含IMAX关键词: ${hasImaxKeyword ? '✅' : '❌'}`);
    
    if (hasChineseChars && hasImaxKeyword) {
        console.log('\n🎉 转换成功！文件已准备好用于进一步处理。');
    } else {
        console.log('\n⚠️ 转换可能不完整，请检查结果文件。');
    }
    
} catch (error) {
    console.error('❌ 转换失败:', error.message);
}
