// 测试页面功能的脚本
console.log('开始测试 IMAX 影院页面功能...');

// 等待页面加载完成
setTimeout(() => {
    console.log('=== 功能测试报告 ===');
    
    // 1. 测试数据加载
    const cinemaCards = document.querySelectorAll('.cinema-card');
    console.log(`✅ 数据加载: ${cinemaCards.length} 家影院`);
    
    // 2. 测试排序按钮
    const sortButtons = document.querySelectorAll('.sortable');
    console.log(`✅ 排序按钮: ${sortButtons.length} 个`);
    
    // 3. 测试搜索框
    const searchInput = document.getElementById('searchInput');
    console.log(`✅ 搜索框: ${searchInput ? '存在' : '缺失'}`);
    
    // 4. 测试表头结构
    const headers = document.querySelectorAll('#cinema-list-header span');
    console.log(`✅ 表头列数: ${headers.length} 列`);
    
    // 5. 测试第一个影院的数据结构
    if (cinemaCards.length > 0) {
        const firstCard = cinemaCards[0];
        const columns = firstCard.children.length;
        console.log(`✅ 数据列数: ${columns} 列`);
        
        // 检查是否有备注列（应该没有）
        const hasRemarks = firstCard.textContent.includes('备注');
        console.log(`✅ 备注列移除: ${!hasRemarks ? '成功' : '失败'}`);
    }
    
    // 6. 测试排序功能
    console.log('🧪 测试排序功能...');
    if (sortButtons.length > 0) {
        // 模拟点击面积排序
        const areaSort = Array.from(sortButtons).find(btn => btn.dataset.sort === 'screenArea');
        if (areaSort) {
            console.log('点击面积排序按钮...');
            areaSort.click();
            
            setTimeout(() => {
                const icon = areaSort.querySelector('.sort-icon');
                console.log(`✅ 排序图标更新: ${icon.textContent}`);
                
                // 再次点击测试降序
                areaSort.click();
                setTimeout(() => {
                    console.log(`✅ 降序排序图标: ${icon.textContent}`);
                    
                    // 第三次点击测试复原
                    areaSort.click();
                    setTimeout(() => {
                        console.log(`✅ 复原排序图标: ${icon.textContent}`);
                        console.log('🎉 所有功能测试完成！');
                    }, 100);
                }, 100);
            }, 100);
        }
    }
    
    // 7. 测试搜索功能
    if (searchInput) {
        console.log('🧪 测试搜索功能...');
        searchInput.value = '上海';
        searchInput.dispatchEvent(new Event('input'));
        
        setTimeout(() => {
            const visibleCards = document.querySelectorAll('.cinema-card:not(.hidden)');
            console.log(`✅ 搜索"上海": 显示 ${visibleCards.length} 个结果`);
            
            // 清空搜索
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('input'));
        }, 100);
    }
    
}, 1000);
