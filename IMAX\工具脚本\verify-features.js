// 验证页面功能的脚本
console.log('🚀 开始验证 IMAX 影院页面功能...');

// 等待页面和数据加载完成
setTimeout(() => {
    console.log('=== 功能验证报告 ===');
    
    // 1. 验证数据加载
    const cinemaCards = document.querySelectorAll('.cinema-card');
    console.log(`✅ 影院数据: ${cinemaCards.length} 家影院`);
    
    if (cinemaCards.length === 0) {
        console.error('❌ 未找到影院数据，可能加载失败');
        return;
    }
    
    // 2. 验证音响系统图标
    const audioIcons = document.querySelectorAll('.audio-feature');
    console.log(`🔊 音响系统图标: ${audioIcons.length} 个`);
    
    // 3. 验证tooltip功能
    if (audioIcons.length > 0) {
        const firstIcon = audioIcons[0];
        const tooltip = firstIcon.querySelector('.tooltip-text');
        if (tooltip) {
            console.log(`✅ Tooltip内容: "${tooltip.textContent}"`);
        }
    }
    
    // 4. 验证排序功能
    const sortButtons = document.querySelectorAll('.sortable');
    console.log(`📊 排序按钮: ${sortButtons.length} 个`);
    
    // 5. 验证统计信息
    const statCards = document.querySelectorAll('.stat-card');
    console.log(`📈 统计卡片: ${statCards.length} 个`);
    
    if (statCards.length > 0) {
        const totalCount = statCards[0].querySelector('.text-3xl').textContent;
        console.log(`📊 总影院数显示: ${totalCount}`);
    }
    
    // 6. 验证搜索功能
    const searchInput = document.getElementById('searchInput');
    console.log(`🔍 搜索框: ${searchInput ? '存在' : '缺失'}`);
    
    // 7. 验证响应式布局
    const header = document.getElementById('cinema-list-header');
    const isDesktop = window.innerWidth >= 768;
    const headerVisible = !header.classList.contains('hidden');
    console.log(`💻 桌面端表头: ${isDesktop && headerVisible ? '显示' : '隐藏'}`);
    
    // 8. 验证数据结构
    if (cinemaCards.length > 0) {
        const firstCard = cinemaCards[0];
        const name = firstCard.querySelector('.cinema-name')?.textContent;
        const projectorType = firstCard.querySelector('.cinema-type-badge')?.textContent;
        
        console.log(`🏢 第一家影院: ${name}`);
        console.log(`🎬 放映机类型: ${projectorType}`);
        
        // 检查是否有12声道音响系统
        const hasAudioIcon = firstCard.querySelector('.audio-feature');
        console.log(`🎵 音响系统: ${hasAudioIcon ? '12声道' : '标准'}`);
    }
    
    // 9. 验证移动端hover修复
    const isMobile = window.innerWidth < 768;
    const hasHover = window.matchMedia('(hover: hover)').matches;
    console.log(`📱 设备类型: ${isMobile ? '移动端' : '桌面端'}`);
    console.log(`🖱️ Hover支持: ${hasHover ? '是' : '否'}`);
    
    // 10. 性能检查
    const loadTime = performance.now();
    console.log(`⚡ 页面加载时间: ${loadTime.toFixed(2)}ms`);
    
    // 总结
    console.log('\n🎉 功能验证完成！');
    console.log(`📊 数据完整性: ${cinemaCards.length >= 170 ? '✅ 完整' : '⚠️ 部分'}`);
    console.log(`🎨 UI功能: ${audioIcons.length > 0 && sortButtons.length > 0 ? '✅ 正常' : '❌ 异常'}`);
    console.log(`📱 响应式: ${header ? '✅ 支持' : '❌ 不支持'}`);
    
}, 2000); // 等待2秒确保数据加载完成
