const fs = require('fs');

console.log('开始处理数字IMAX CSV文件...');

try {
    // 读取GBK编码的文件
    const iconv = require('iconv-lite');
    const buffer = fs.readFileSync('../数字IMAX.csv');
    const csvContent = iconv.decode(buffer, 'gbk');
    
    console.log('✅ 成功读取GBK编码文件');
    console.log('文件前100字符:', csvContent.substring(0, 100));
    
    // 分割行
    const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    console.log(`总行数: ${lines.length}`);
    
    // 跳过表头
    const dataLines = lines.slice(1);
    
    // 解析数据
    const cinemas = [];
    let i = 0;
    
    while (i < dataLines.length) {
        const line = dataLines[i];
        
        // 跳过空行
        if (!line) {
            i++;
            continue;
        }
        
        // 解析基本信息行
        const parts = line.split(',');
        if (parts.length < 2) {
            i++;
            continue;
        }
        
        const cinema = {
            name: parts[0].trim(),
            projectorType: parts[1].trim(),
            screenWidths: [],
            screenHeights: [],
            screenAreas: [],
            seatCounts: []
        };
        
        // 解析银幕数据
        if (parts[2]) {
            const widthData = parts[2].replace(/"/g, '').trim();
            if (widthData) {
                cinema.screenWidths = widthData.split('\n').map(v => v.trim()).filter(v => v && !isNaN(parseFloat(v)));
            }
        }
        
        if (parts[3]) {
            const heightData = parts[3].replace(/"/g, '').trim();
            if (heightData) {
                cinema.screenHeights = heightData.split('\n').map(v => v.trim()).filter(v => v && !isNaN(parseFloat(v)));
            }
        }
        
        if (parts[4]) {
            const areaData = parts[4].replace(/"/g, '').trim();
            if (areaData) {
                cinema.screenAreas = areaData.split('\n').map(v => v.trim()).filter(v => v && !isNaN(parseFloat(v)));
            }
        }
        
        if (parts[5]) {
            const seatData = parts[5].replace(/"/g, '').trim();
            if (seatData) {
                cinema.seatCounts = seatData.split('\n').map(v => v.trim()).filter(v => v && !isNaN(parseInt(v)));
            }
        }
        
        // 检查是否有多行数据
        let j = i + 1;
        while (j < dataLines.length) {
            const nextLine = dataLines[j];
            if (!nextLine || nextLine.includes(',')) {
                // 如果下一行是空行或包含逗号（新记录），停止
                if (nextLine && nextLine.split(',').length >= 2 && nextLine.split(',')[0].trim() && nextLine.split(',')[1].trim()) {
                    break;
                }
            }
            
            // 这是续行数据
            const continuationParts = nextLine.split(',');
            if (continuationParts.length >= 4) {
                if (continuationParts[0] && !isNaN(parseFloat(continuationParts[0]))) {
                    cinema.screenWidths.push(continuationParts[0].trim());
                }
                if (continuationParts[1] && !isNaN(parseFloat(continuationParts[1]))) {
                    cinema.screenHeights.push(continuationParts[1].trim());
                }
                if (continuationParts[2] && !isNaN(parseFloat(continuationParts[2]))) {
                    cinema.screenAreas.push(continuationParts[2].trim());
                }
                if (continuationParts[3] && !isNaN(parseInt(continuationParts[3]))) {
                    cinema.seatCounts.push(continuationParts[3].trim());
                }
            }
            j++;
        }
        
        // 处理影院数据
        const processedCinema = processCinemaData(cinema);
        if (processedCinema.name) {
            cinemas.push(processedCinema);
        }
        
        i = j;
    }
    
    console.log(`成功解析 ${cinemas.length} 家影院`);
    
    // 显示前5个示例
    console.log('\n前5个影院示例:');
    cinemas.slice(0, 5).forEach((cinema, index) => {
        console.log(`${index + 1}. ${cinema.name}`);
        console.log(`   类型: ${cinema.projectorType}`);
        console.log(`   银幕: ${cinema.screenWidth}m × ${cinema.screenHeight}m (${cinema.screenArea}㎡)`);
        console.log(`   座位: ${cinema.seatCount}`);
        console.log(`   音响: ${cinema.audioSystem || '无'}`);
        console.log('');
    });
    
    // 转换为目标JSON格式
    const jsonData = cinemas.map(cinema => ({
        name: cinema.name,
        projectorType: cinema.projectorType,
        audioSystem: cinema.audioSystem,
        screenArea: cinema.screenArea,
        screenWidth: cinema.screenWidth,
        screenHeight: cinema.screenHeight,
        seatCount: cinema.seatCount,
        openDate: "--",
        remarks: ""
    }));
    
    // 保存为JSON
    const jsonContent = JSON.stringify(jsonData, null, 2);
    fs.writeFileSync('../数字IMAX.json', jsonContent, 'utf8');
    
    console.log('✅ 成功保存到 数字IMAX.json');
    
    // 生成统计信息
    const stats = {
        total: cinemas.length,
        with12Channel: cinemas.filter(c => c.audioSystem && c.audioSystem.includes('12声道')).length,
        withoutScreenData: cinemas.filter(c => c.screenArea === '--').length,
        withoutSeatData: cinemas.filter(c => c.seatCount === '--').length
    };
    
    console.log('\n📊 统计信息:');
    console.log(`总数: ${stats.total}`);
    console.log(`12声道音响: ${stats.with12Channel}`);
    console.log(`缺少银幕数据: ${stats.withoutScreenData}`);
    console.log(`缺少座位数据: ${stats.withoutSeatData}`);
    
    console.log('\n🎉 转换完成！');
    
} catch (error) {
    console.error('❌ 转换失败:', error.message);
    if (error.message.includes('iconv-lite')) {
        console.log('\n💡 提示: 需要安装 iconv-lite 库');
        console.log('运行: npm install iconv-lite');
    }
}

// 处理影院数据的函数
function processCinemaData(rawCinema) {
    // 取最后一行数据作为准确数据
    const getLastValue = (dataArray) => {
        if (!dataArray || dataArray.length === 0) return '--';
        const lastValue = dataArray[dataArray.length - 1];
        return lastValue && lastValue !== '' ? lastValue : '--';
    };
    
    const screenWidth = getLastValue(rawCinema.screenWidths);
    const screenHeight = getLastValue(rawCinema.screenHeights);
    const screenArea = getLastValue(rawCinema.screenAreas);
    const seatCount = getLastValue(rawCinema.seatCounts);
    
    // 特殊处理12声道音响系统
    // 只有上海幸福蓝海国际影城（宝山龙湖IMAX店）拥有
    const audioSystem = rawCinema.name.includes('上海幸福蓝海国际影城（宝山龙湖IMAX店）') 
        ? '12声道音响系统' 
        : '';
    
    return {
        name: rawCinema.name,
        projectorType: rawCinema.projectorType,
        audioSystem: audioSystem,
        screenWidth: screenWidth,
        screenHeight: screenHeight,
        screenArea: screenArea,
        seatCount: seatCount
    };
}
