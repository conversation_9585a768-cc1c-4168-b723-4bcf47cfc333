const fs = require('fs');

console.log('开始处理数字IMAX CSV文件...');

try {
    // 读取GBK编码的文件
    const iconv = require('iconv-lite');
    const buffer = fs.readFileSync('../数字IMAX.csv');
    const csvContent = iconv.decode(buffer, 'gbk');
    
    console.log('✅ 成功读取GBK编码文件');
    
    // 分割行
    const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    console.log(`总行数: ${lines.length}`);
    
    // 跳过表头
    const dataLines = lines.slice(1);
    
    // 手动解析数据
    const cinemas = [];
    let currentCinema = null;
    
    for (let i = 0; i < dataLines.length; i++) {
        const line = dataLines[i];
        
        // 检查是否是新的影院记录（包含影院名称和IMAX类型）
        if (line.includes('IMAX') && (line.includes('影城') || line.includes('影院') || line.includes('CINEMA') || line.includes('cinema'))) {
            // 保存之前的影院
            if (currentCinema && currentCinema.name) {
                cinemas.push(processCinemaData(currentCinema));
            }
            
            // 解析新影院
            const parts = line.split(',');
            if (parts.length >= 2) {
                currentCinema = {
                    name: parts[0].trim(),
                    projectorType: parts[1].trim(),
                    screenWidths: [],
                    screenHeights: [],
                    screenAreas: [],
                    seatCounts: []
                };
                
                // 解析当前行的数据
                parseDataFromLine(line, currentCinema);
            }
        } else if (currentCinema) {
            // 这是数据行，解析数值
            parseDataFromLine(line, currentCinema);
        }
    }
    
    // 处理最后一个影院
    if (currentCinema && currentCinema.name) {
        cinemas.push(processCinemaData(currentCinema));
    }
    
    console.log(`成功解析 ${cinemas.length} 家影院`);
    
    // 显示前5个示例
    console.log('\n前5个影院示例:');
    cinemas.slice(0, 5).forEach((cinema, index) => {
        console.log(`${index + 1}. ${cinema.name}`);
        console.log(`   类型: ${cinema.projectorType}`);
        console.log(`   银幕: ${cinema.screenWidth}m × ${cinema.screenHeight}m (${cinema.screenArea}㎡)`);
        console.log(`   座位: ${cinema.seatCount}`);
        console.log(`   音响: ${cinema.audioSystem || '无'}`);
        console.log('');
    });
    
    // 转换为目标JSON格式
    const jsonData = cinemas.map(cinema => ({
        name: cinema.name,
        projectorType: cinema.projectorType,
        audioSystem: cinema.audioSystem,
        screenArea: cinema.screenArea,
        screenWidth: cinema.screenWidth,
        screenHeight: cinema.screenHeight,
        seatCount: cinema.seatCount,
        openDate: "--",
        remarks: ""
    }));
    
    // 保存为JSON
    const jsonContent = JSON.stringify(jsonData, null, 2);
    fs.writeFileSync('../数字IMAX.json', jsonContent, 'utf8');
    
    console.log('✅ 成功保存到 数字IMAX.json');
    
    // 生成统计信息
    const stats = {
        total: cinemas.length,
        with12Channel: cinemas.filter(c => c.audioSystem && c.audioSystem.includes('12声道')).length,
        withoutScreenData: cinemas.filter(c => c.screenArea === '--').length,
        withoutSeatData: cinemas.filter(c => c.seatCount === '--').length
    };
    
    console.log('\n📊 统计信息:');
    console.log(`总数: ${stats.total}`);
    console.log(`12声道音响: ${stats.with12Channel}`);
    console.log(`缺少银幕数据: ${stats.withoutScreenData}`);
    console.log(`缺少座位数据: ${stats.withoutSeatData}`);
    
    console.log('\n🎉 转换完成！');
    
} catch (error) {
    console.error('❌ 转换失败:', error.message);
    console.error(error.stack);
}

// 从行中解析数据
function parseDataFromLine(line, cinema) {
    // 移除引号并分割
    const cleanLine = line.replace(/"/g, '');
    const parts = cleanLine.split(',');
    
    for (const part of parts) {
        const trimmed = part.trim();
        if (!trimmed) continue;
        
        const num = parseFloat(trimmed);
        if (!isNaN(num)) {
            // 根据数值范围判断类型
            if (num >= 10 && num <= 30) {
                // 可能是宽度
                cinema.screenWidths.push(trimmed);
            } else if (num >= 5 && num <= 20) {
                // 可能是高度
                cinema.screenHeights.push(trimmed);
            } else if (num >= 50 && num <= 600) {
                // 可能是面积
                cinema.screenAreas.push(trimmed);
            } else if (num >= 100 && num <= 1000) {
                // 可能是座位数
                cinema.seatCounts.push(trimmed);
            }
        }
    }
}

// 处理影院数据的函数
function processCinemaData(rawCinema) {
    // 取最后一行数据作为准确数据
    const getLastValue = (dataArray) => {
        if (!dataArray || dataArray.length === 0) return '--';
        const lastValue = dataArray[dataArray.length - 1];
        return lastValue && lastValue !== '' ? lastValue : '--';
    };
    
    const screenWidth = getLastValue(rawCinema.screenWidths);
    const screenHeight = getLastValue(rawCinema.screenHeights);
    const screenArea = getLastValue(rawCinema.screenAreas);
    const seatCount = getLastValue(rawCinema.seatCounts);
    
    // 特殊处理12声道音响系统
    // 只有上海幸福蓝海国际影城（宝山龙湖IMAX店）拥有
    const audioSystem = rawCinema.name.includes('上海幸福蓝海国际影城（宝山龙湖IMAX店）') 
        ? '12声道音响系统' 
        : '';
    
    return {
        name: rawCinema.name,
        projectorType: rawCinema.projectorType,
        audioSystem: audioSystem,
        screenWidth: screenWidth,
        screenHeight: screenHeight,
        screenArea: screenArea,
        seatCount: seatCount
    };
}
