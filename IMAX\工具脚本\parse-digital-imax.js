const fs = require('fs');

console.log('开始处理数字IMAX CSV文件...');

try {
    // 读取GBK编码的文件
    const iconv = require('iconv-lite');
    const buffer = fs.readFileSync('../数字IMAX.csv');
    const csvContent = iconv.decode(buffer, 'gbk');
    
    console.log('✅ 成功读取GBK编码文件');
    
    // 分割行
    const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    console.log(`总行数: ${lines.length}`);
    
    // 跳过表头
    const dataLines = lines.slice(1);
    
    // 解析数据
    const cinemas = [];
    let i = 0;
    
    while (i < dataLines.length) {
        const line = dataLines[i];
        
        // 跳过空行或无效行
        if (!line || !line.includes(',')) {
            i++;
            continue;
        }
        
        // 分割CSV行
        const parts = splitCSVLine(line);
        
        // 检查是否是有效的影院记录
        if (parts.length >= 2 && parts[0] && parts[1] && parts[1].includes('IMAX')) {
            const cinema = {
                name: parts[0].trim(),
                projectorType: parts[1].trim(),
                screenWidths: [],
                screenHeights: [],
                screenAreas: [],
                seatCounts: []
            };
            
            // 解析数据列
            if (parts[2]) {
                cinema.screenWidths = extractNumbers(parts[2]);
            }
            if (parts[3]) {
                cinema.screenHeights = extractNumbers(parts[3]);
            }
            if (parts[4]) {
                cinema.screenAreas = extractNumbers(parts[4]);
            }
            if (parts[5]) {
                cinema.seatCounts = extractNumbers(parts[5]);
            }
            
            // 检查后续行是否有续行数据
            let j = i + 1;
            while (j < dataLines.length) {
                const nextLine = dataLines[j];
                if (!nextLine) {
                    j++;
                    continue;
                }
                
                // 如果下一行是新的影院记录，停止
                const nextParts = splitCSVLine(nextLine);
                if (nextParts.length >= 2 && nextParts[0] && nextParts[1] && nextParts[1].includes('IMAX')) {
                    break;
                }
                
                // 如果是数据行，解析数据
                if (nextLine.includes(',')) {
                    const dataParts = splitCSVLine(nextLine);
                    if (dataParts[0]) cinema.screenWidths.push(...extractNumbers(dataParts[0]));
                    if (dataParts[1]) cinema.screenHeights.push(...extractNumbers(dataParts[1]));
                    if (dataParts[2]) cinema.screenAreas.push(...extractNumbers(dataParts[2]));
                    if (dataParts[3]) cinema.seatCounts.push(...extractNumbers(dataParts[3]));
                } else {
                    // 单个数值行
                    const nums = extractNumbers(nextLine);
                    if (nums.length > 0) {
                        const num = parseFloat(nums[0]);
                        if (num >= 10 && num <= 30) {
                            cinema.screenWidths.push(nums[0]);
                        } else if (num >= 5 && num <= 20) {
                            cinema.screenHeights.push(nums[0]);
                        } else if (num >= 50 && num <= 600) {
                            cinema.screenAreas.push(nums[0]);
                        } else if (num >= 100 && num <= 1000) {
                            cinema.seatCounts.push(nums[0]);
                        }
                    }
                }
                j++;
            }
            
            // 处理并添加影院
            const processedCinema = processCinemaData(cinema);
            cinemas.push(processedCinema);
            
            i = j;
        } else {
            i++;
        }
    }
    
    console.log(`成功解析 ${cinemas.length} 家影院`);
    
    // 显示前5个示例
    console.log('\n前5个影院示例:');
    cinemas.slice(0, 5).forEach((cinema, index) => {
        console.log(`${index + 1}. ${cinema.name}`);
        console.log(`   类型: ${cinema.projectorType}`);
        console.log(`   银幕: ${cinema.screenWidth}m × ${cinema.screenHeight}m (${cinema.screenArea}㎡)`);
        console.log(`   座位: ${cinema.seatCount}`);
        console.log(`   音响: ${cinema.audioSystem || '无'}`);
        console.log('');
    });
    
    // 转换为目标JSON格式
    const jsonData = cinemas.map(cinema => ({
        name: cinema.name,
        projectorType: cinema.projectorType,
        audioSystem: cinema.audioSystem,
        screenArea: cinema.screenArea,
        screenWidth: cinema.screenWidth,
        screenHeight: cinema.screenHeight,
        seatCount: cinema.seatCount,
        openDate: "--",
        remarks: ""
    }));
    
    // 保存为JSON
    const jsonContent = JSON.stringify(jsonData, null, 2);
    fs.writeFileSync('../数字IMAX.json', jsonContent, 'utf8');
    
    console.log('✅ 成功保存到 数字IMAX.json');
    
    // 生成统计信息
    const stats = {
        total: cinemas.length,
        with12Channel: cinemas.filter(c => c.audioSystem && c.audioSystem.includes('12声道')).length,
        withoutScreenData: cinemas.filter(c => c.screenArea === '--').length,
        withoutSeatData: cinemas.filter(c => c.seatCount === '--').length
    };
    
    console.log('\n📊 统计信息:');
    console.log(`总数: ${stats.total}`);
    console.log(`12声道音响: ${stats.with12Channel}`);
    console.log(`缺少银幕数据: ${stats.withoutScreenData}`);
    console.log(`缺少座位数据: ${stats.withoutSeatData}`);
    
    console.log('\n🎉 转换完成！');
    
} catch (error) {
    console.error('❌ 转换失败:', error.message);
    console.error(error.stack);
}

// 分割CSV行
function splitCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current);
            current = '';
        } else {
            current += char;
        }
    }
    result.push(current);
    
    return result;
}

// 从字符串中提取数字
function extractNumbers(str) {
    if (!str) return [];

    // 移除引号并按换行分割
    const cleaned = str.replace(/"/g, '').trim();
    const lines = cleaned.split('\n').map(line => line.trim()).filter(line => line);

    const numbers = [];
    for (const line of lines) {
        // 处理可能包含逗号的情况
        const parts = line.split(',');
        for (const part of parts) {
            const trimmed = part.trim();
            const num = parseFloat(trimmed);
            if (!isNaN(num) && trimmed !== '') {
                numbers.push(trimmed);
            }
        }
    }

    return numbers;
}

// 处理影院数据的函数
function processCinemaData(rawCinema) {
    // 取最后一行数据作为准确数据
    const getLastValue = (dataArray) => {
        if (!dataArray || dataArray.length === 0) return '--';
        const lastValue = dataArray[dataArray.length - 1];
        return lastValue && lastValue !== '' ? lastValue : '--';
    };
    
    const screenWidth = getLastValue(rawCinema.screenWidths);
    const screenHeight = getLastValue(rawCinema.screenHeights);
    const screenArea = getLastValue(rawCinema.screenAreas);
    const seatCount = getLastValue(rawCinema.seatCounts);
    
    // 特殊处理12声道音响系统
    // 只有上海幸福蓝海国际影城（宝山龙湖IMAX店）拥有
    const audioSystem = rawCinema.name.includes('上海幸福蓝海国际影城（宝山龙湖IMAX店）') 
        ? '12声道音响系统' 
        : '';
    
    return {
        name: rawCinema.name,
        projectorType: rawCinema.projectorType,
        audioSystem: audioSystem,
        screenWidth: screenWidth,
        screenHeight: screenHeight,
        screenArea: screenArea,
        seatCount: seatCount
    };
}
