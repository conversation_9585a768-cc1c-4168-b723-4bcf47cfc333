const fs = require('fs');

console.log('开始处理数字IMAX CSV文件...');

try {
    // 尝试不同的编码方式读取文件
    let csvContent;
    
    try {
        // 首先尝试以GBK编码读取
        const iconv = require('iconv-lite');
        const buffer = fs.readFileSync('../数字IMAX.csv');
        csvContent = iconv.decode(buffer, 'gbk');
        console.log('✅ 成功以GBK编码读取文件');
    } catch (error) {
        console.log('⚠️ iconv-lite未安装，尝试其他方法...');
        // 如果没有iconv-lite，尝试直接读取
        csvContent = fs.readFileSync('../数字IMAX.csv', 'utf8');
    }
    
    console.log('文件前100字符:', csvContent.substring(0, 100));
    
    // 分割行并过滤空行
    const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    console.log(`总行数: ${lines.length}`);
    
    // 解析表头
    const headerLine = lines[0];
    console.log('表头行:', headerLine);
    
    // 重新组合被分割的行
    const reconstructedLines = [];
    let currentLine = '';
    let inQuotes = false;

    for (let i = 1; i < lines.length; i++) {
        const line = lines[i];

        // 检查引号状态
        for (const char of line) {
            if (char === '"') {
                inQuotes = !inQuotes;
            }
        }

        if (currentLine) {
            currentLine += '\n' + line;
        } else {
            currentLine = line;
        }

        // 如果不在引号内，且行包含足够的逗号，认为是完整行
        if (!inQuotes && currentLine.split(',').length >= 6) {
            reconstructedLines.push(currentLine);
            currentLine = '';
        }
    }

    // 添加最后一行
    if (currentLine) {
        reconstructedLines.push(currentLine);
    }

    console.log(`重组后行数: ${reconstructedLines.length}`);

    // 解析数据
    const cinemas = [];

    for (const line of reconstructedLines) {
        const values = parseCSVLine(line);

        if (values.length >= 2 && values[0] && values[1]) {
            const cinema = {
                name: values[0].trim(),
                projectorType: values[1].trim(),
                screenWidthData: [],
                screenHeightData: [],
                screenAreaData: [],
                seatCountData: []
            };

            // 解析多行数据
            if (values[2]) {
                cinema.screenWidthData = values[2].split('\n').map(v => v.trim()).filter(v => v);
            }
            if (values[3]) {
                cinema.screenHeightData = values[3].split('\n').map(v => v.trim()).filter(v => v);
            }
            if (values[4]) {
                cinema.screenAreaData = values[4].split('\n').map(v => v.trim()).filter(v => v);
            }
            if (values[5]) {
                cinema.seatCountData = values[5].split('\n').map(v => v.trim()).filter(v => v);
            }

            cinemas.push(processCinemaData(cinema));
        }
    }

    
    console.log(`成功解析 ${cinemas.length} 家影院`);
    
    // 显示前3个示例
    console.log('\n前3个影院示例:');
    cinemas.slice(0, 3).forEach((cinema, index) => {
        console.log(`${index + 1}. ${cinema.name}`);
        console.log(`   类型: ${cinema.projectorType}`);
        console.log(`   银幕: ${cinema.screenWidth}m × ${cinema.screenHeight}m (${cinema.screenArea}㎡)`);
        console.log(`   座位: ${cinema.seatCount}`);
        console.log(`   音响: ${cinema.audioSystem}`);
        console.log('');
    });
    
    // 转换为目标JSON格式
    const jsonData = cinemas.map(cinema => ({
        name: cinema.name,
        projectorType: cinema.projectorType,
        audioSystem: cinema.audioSystem,
        screenArea: cinema.screenArea,
        screenWidth: cinema.screenWidth,
        screenHeight: cinema.screenHeight,
        seatCount: cinema.seatCount,
        openDate: "--",
        remarks: ""
    }));
    
    // 保存为JSON
    const jsonContent = JSON.stringify(jsonData, null, 2);
    fs.writeFileSync('../数字IMAX.json', jsonContent, 'utf8');
    
    console.log('✅ 成功保存到 数字IMAX.json');
    
    // 生成统计信息
    const stats = {
        total: cinemas.length,
        with12Channel: cinemas.filter(c => c.audioSystem.includes('12声道')).length,
        withoutScreenData: cinemas.filter(c => c.screenArea === '--').length
    };
    
    console.log('\n📊 统计信息:');
    console.log(`总数: ${stats.total}`);
    console.log(`12声道音响: ${stats.with12Channel}`);
    console.log(`缺少银幕数据: ${stats.withoutScreenData}`);
    
    console.log('\n🎉 转换完成！');
    
} catch (error) {
    console.error('❌ 转换失败:', error.message);
    console.error(error.stack);
}

// 解析CSV行的函数
function parseCSVLine(line) {
    const values = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            values.push(current);
            current = '';
        } else {
            current += char;
        }
    }
    values.push(current);
    
    return values;
}

// 处理影院数据的函数
function processCinemaData(rawCinema) {
    // 取最后一行数据作为准确数据
    const getLastValue = (dataArray) => {
        if (!dataArray || dataArray.length === 0) return '--';
        const lastValue = dataArray[dataArray.length - 1];
        return lastValue && lastValue !== '' ? lastValue : '--';
    };
    
    const screenWidth = getLastValue(rawCinema.screenWidthData);
    const screenHeight = getLastValue(rawCinema.screenHeightData);
    const screenArea = getLastValue(rawCinema.screenAreaData);
    const seatCount = getLastValue(rawCinema.seatCountData);
    
    // 特殊处理12声道音响系统
    // 只有上海幸福蓝海国际影城（宝山龙湖IMAX店）拥有
    const audioSystem = rawCinema.name.includes('上海幸福蓝海国际影城（宝山龙湖IMAX店）') 
        ? '12声道音响系统' 
        : '';
    
    return {
        name: rawCinema.name,
        projectorType: rawCinema.projectorType,
        audioSystem: audioSystem,
        screenWidth: screenWidth,
        screenHeight: screenHeight,
        screenArea: screenArea,
        seatCount: seatCount
    };
}
