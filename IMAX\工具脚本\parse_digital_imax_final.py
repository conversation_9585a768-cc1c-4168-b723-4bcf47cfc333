#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def main():
    print("开始处理数字IMAX CSV文件...")
    
    try:
        # 读取CSV文件
        with open('../数字IMAX.csv', 'r', encoding='gbk') as file:
            content = file.read()
            print("✅ 成功读取GBK编码文件")
        
        # 分割行
        lines = content.split('\n')
        print(f"总行数: {len(lines)}")
        
        # 跳过表头
        data_lines = lines[1:]
        
        cinemas = []
        i = 0
        
        while i < len(data_lines):
            line = data_lines[i].strip()
            
            # 跳过空行
            if not line:
                i += 1
                continue
            
            # 检查是否是新影院记录的开始（包含影院名称和IMAX类型）
            if 'IMAX' in line and ('影城' in line or '影院' in line or 'CINEMA' in line or 'cinema' in line):
                # 重建完整的CSV记录
                full_record = line
                j = i + 1
                
                # 检查引号是否配对
                quote_count = line.count('"')
                
                # 如果引号数量是奇数，说明记录跨越多行
                while quote_count % 2 != 0 and j < len(data_lines):
                    next_line = data_lines[j].strip()
                    if next_line:
                        full_record += '\n' + next_line
                        quote_count += next_line.count('"')
                    j += 1
                
                # 解析完整记录
                cinema = parse_cinema_record(full_record)
                if cinema:
                    cinemas.append(cinema)
                
                i = j
            else:
                i += 1
        
        print(f"成功解析 {len(cinemas)} 家影院")
        
        # 显示前5个示例
        print("\n前5个影院示例:")
        for idx, cinema in enumerate(cinemas[:5], 1):
            print(f"{idx}. {cinema['name']}")
            print(f"   类型: {cinema['projectorType']}")
            print(f"   银幕: {cinema['screenWidth']}m × {cinema['screenHeight']}m ({cinema['screenArea']}㎡)")
            print(f"   座位: {cinema['seatCount']}")
            print(f"   音响: {cinema['audioSystem'] or '无'}")
            print()
        
        # 保存为JSON
        with open('../数字IMAX.json', 'w', encoding='utf-8') as f:
            json.dump(cinemas, f, ensure_ascii=False, indent=2)
        
        print("✅ 成功保存到 数字IMAX.json")
        
        # 生成统计信息
        stats = {
            'total': len(cinemas),
            'with_12_channel': len([c for c in cinemas if '12声道' in c['audioSystem']]),
            'without_screen_data': len([c for c in cinemas if c['screenArea'] == '--']),
            'without_seat_data': len([c for c in cinemas if c['seatCount'] == '--'])
        }
        
        print("\n📊 统计信息:")
        print(f"总数: {stats['total']}")
        print(f"12声道音响: {stats['with_12_channel']}")
        print(f"缺少银幕数据: {stats['without_screen_data']}")
        print(f"缺少座位数据: {stats['without_seat_data']}")
        
        print("\n🎉 转换完成！")
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        import traceback
        traceback.print_exc()

def parse_cinema_record(record):
    """解析单个影院记录"""
    try:
        # 手动解析CSV字段
        fields = []
        current_field = ""
        in_quotes = False
        
        i = 0
        while i < len(record):
            char = record[i]
            
            if char == '"':
                in_quotes = not in_quotes
            elif char == ',' and not in_quotes:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            i += 1
        
        # 添加最后一个字段
        fields.append(current_field.strip())
        
        # 确保至少有6个字段
        while len(fields) < 6:
            fields.append("")
        
        # 提取基本信息
        name = fields[0].strip()
        projector_type = fields[1].strip()
        
        # 解析多行数据
        screen_widths = extract_numbers(fields[2])
        screen_heights = extract_numbers(fields[3])
        screen_areas = extract_numbers(fields[4])
        seat_counts = extract_numbers(fields[5])
        
        # 取最后一行数据作为准确数据
        screen_width = get_last_value(screen_widths)
        screen_height = get_last_value(screen_heights)
        screen_area = get_last_value(screen_areas)
        seat_count = get_last_value(seat_counts)
        
        # 特殊处理12声道音响系统
        audio_system = ""
        if "上海幸福蓝海国际影城（宝山龙湖IMAX店）" in name:
            audio_system = "12声道音响系统"
        
        return {
            "name": name,
            "projectorType": projector_type,
            "audioSystem": audio_system,
            "screenArea": screen_area,
            "screenWidth": screen_width,
            "screenHeight": screen_height,
            "seatCount": seat_count,
            "openDate": "--",
            "remarks": ""
        }
        
    except Exception as e:
        print(f"解析记录失败: {record[:50]}... 错误: {e}")
        return None

def extract_numbers(field_str):
    """从字段中提取数字"""
    if not field_str:
        return []
    
    # 移除引号
    cleaned = field_str.replace('"', '').strip()
    
    # 按换行分割
    lines = [line.strip() for line in cleaned.split('\n') if line.strip()]
    
    numbers = []
    for line in lines:
        # 尝试解析为数字
        try:
            # 移除可能的逗号和其他字符
            clean_line = re.sub(r'[^\d.]', '', line)
            if clean_line:
                num = float(clean_line)
                numbers.append(str(num))
        except ValueError:
            continue
    
    return numbers

def get_last_value(data_list):
    """获取列表中的最后一个值"""
    if not data_list:
        return '--'
    return data_list[-1] if data_list[-1] else '--'

if __name__ == "__main__":
    main()
