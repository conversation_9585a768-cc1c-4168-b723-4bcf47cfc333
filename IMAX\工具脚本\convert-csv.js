const fs = require('fs');

// 尝试不同的编码读取文件
const encodings = ['utf8', 'latin1', 'ascii', 'utf16le', 'ucs2', 'base64', 'hex'];

console.log('尝试读取 imax_cinemas.csv...');

let content = null;
let workingEncoding = null;

// 首先尝试直接读取二进制数据
const buffer = fs.readFileSync('imax_cinemas.csv');
console.log('文件大小:', buffer.length, '字节');
console.log('前20个字节:', Array.from(buffer.slice(0, 20)).map(b => b.toString(16).padStart(2, '0')).join(' '));

// 尝试不同编码
for (const encoding of encodings) {
    try {
        const testContent = buffer.toString(encoding);
        console.log(`\n尝试编码 ${encoding}:`);
        console.log('前100个字符:', testContent.substring(0, 100));
        
        // 检查是否包含中文字符或IMAX
        if (testContent.includes('IMAX') && (testContent.includes('影') || testContent.includes('上海') || testContent.includes('北京'))) {
            content = testContent;
            workingEncoding = encoding;
            console.log(`✅ 成功使用编码: ${encoding}`);
            break;
        }
    } catch (error) {
        console.log(`❌ 编码 ${encoding} 失败:`, error.message);
    }
}

// 如果还是没找到，尝试 GBK/GB2312 (使用 latin1 然后手动转换)
if (!content) {
    console.log('\n尝试 GBK/GB2312 编码...');
    try {
        // 读取为 latin1，然后尝试转换
        const latin1Content = buffer.toString('latin1');
        
        // 创建一个简单的 GBK 到 UTF-8 的映射（部分常用字符）
        const gbkMap = {
            'Ӱ': '影',
            '��': '城',
            '��': '名',
            '��': '称',
            '��': '放',
            '��': '映',
            '��': '机',
            '��': '型',
            '��': '号',
            '��': '声',
            '��': '道',
            '��': '音',
            '��': '响',
            '��': '系',
            '��': '统',
            '��': '银',
            '��': '幕',
            '��': '面',
            '��': '积',
            '��': '平',
            '��': '方',
            '��': '米',
            '��': '宽',
            '��': '度',
            '��': '高',
            '��': '座',
            '��': '位',
            '��': '数',
            '�Ϻ�': '上海',
            '����': '万达',
            '��': '北',
            '��': '京',
            '��': '深',
            '��': '圳',
            '��': '广',
            '��': '州'
        };
        
        let convertedContent = latin1Content;
        for (const [gbk, utf8] of Object.entries(gbkMap)) {
            convertedContent = convertedContent.replace(new RegExp(gbk, 'g'), utf8);
        }
        
        if (convertedContent.includes('影城') || convertedContent.includes('上海')) {
            content = convertedContent;
            workingEncoding = 'gbk-converted';
            console.log('✅ 成功转换 GBK 编码');
        }
    } catch (error) {
        console.log('❌ GBK 转换失败:', error.message);
    }
}

if (content) {
    // 保存为 UTF-8
    fs.writeFileSync('imax_cinemas_utf8.csv', content, 'utf8');
    console.log('\n✅ 已保存为 UTF-8 编码: imax_cinemas_utf8.csv');
    
    // 显示前几行
    const lines = content.split('\n').slice(0, 5);
    console.log('\n前5行内容:');
    lines.forEach((line, index) => {
        console.log(`${index + 1}: ${line}`);
    });
    
    // 统计信息
    const totalLines = content.split('\n').filter(line => line.trim()).length;
    console.log(`\n📊 总行数: ${totalLines}`);
    
} else {
    console.log('\n❌ 无法找到合适的编码读取文件');
    console.log('请尝试使用文本编辑器手动转换编码，或提供正确编码的文件');
}
