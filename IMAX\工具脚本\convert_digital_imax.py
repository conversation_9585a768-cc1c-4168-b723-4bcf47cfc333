#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import json
import re
from typing import List, Dict, Any

def parse_multiline_data(data_str: str) -> List[str]:
    """解析可能包含多行数据的字符串"""
    if not data_str:
        return []
    
    # 移除引号并按换行分割
    cleaned = data_str.strip().replace('"', '')
    lines = [line.strip() for line in cleaned.split('\n') if line.strip()]
    
    # 提取数字
    numbers = []
    for line in lines:
        # 尝试解析为浮点数
        try:
            num = float(line)
            numbers.append(str(num))
        except ValueError:
            # 如果不是数字，跳过
            continue
    
    return numbers

def process_cinema_data(cinema_data: Dict) -> Dict[str, Any]:
    """处理影院数据，取最后一行作为准确数据"""
    
    def get_last_value(data_list: List[str]) -> str:
        """获取列表中的最后一个值"""
        if not data_list:
            return '--'
        return data_list[-1] if data_list[-1] else '--'
    
    # 取最后一行数据
    screen_width = get_last_value(cinema_data['screen_widths'])
    screen_height = get_last_value(cinema_data['screen_heights'])
    screen_area = get_last_value(cinema_data['screen_areas'])
    seat_count = get_last_value(cinema_data['seat_counts'])
    
    # 特殊处理12声道音响系统
    # 只有上海幸福蓝海国际影城（宝山龙湖IMAX店）拥有
    audio_system = ""
    if "上海幸福蓝海国际影城（宝山龙湖IMAX店）" in cinema_data['name']:
        audio_system = "12声道音响系统"
    
    return {
        "name": cinema_data['name'],
        "projectorType": cinema_data['projector_type'],
        "audioSystem": audio_system,
        "screenArea": screen_area,
        "screenWidth": screen_width,
        "screenHeight": screen_height,
        "seatCount": seat_count,
        "openDate": "--",
        "remarks": ""
    }

def main():
    print("开始处理数字IMAX CSV文件...")
    
    try:
        # 读取CSV文件
        cinemas = []
        
        with open('../数字IMAX.csv', 'r', encoding='gbk') as file:
            content = file.read()
            print("✅ 成功读取GBK编码文件")
            
            # 分割行
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            print(f"总行数: {len(lines)}")
            
            # 跳过表头
            data_lines = lines[1:]
            
            i = 0
            while i < len(data_lines):
                line = data_lines[i]
                
                # 跳过空行
                if not line:
                    i += 1
                    continue
                
                # 手动解析CSV行（处理引号内的换行）
                parts = []
                current_part = ""
                in_quotes = False
                
                j = 0
                while j < len(line):
                    char = line[j]
                    if char == '"':
                        in_quotes = not in_quotes
                        current_part += char
                    elif char == ',' and not in_quotes:
                        parts.append(current_part)
                        current_part = ""
                    else:
                        current_part += char
                    j += 1
                
                # 添加最后一部分
                parts.append(current_part)
                
                # 检查是否是有效的影院记录
                if len(parts) >= 2 and parts[0] and parts[1] and 'IMAX' in parts[1]:
                    cinema = {
                        'name': parts[0].strip(),
                        'projector_type': parts[1].strip(),
                        'screen_widths': [],
                        'screen_heights': [],
                        'screen_areas': [],
                        'seat_counts': []
                    }
                    
                    # 解析当前行的数据
                    if len(parts) > 2 and parts[2]:
                        cinema['screen_widths'] = parse_multiline_data(parts[2])
                    if len(parts) > 3 and parts[3]:
                        cinema['screen_heights'] = parse_multiline_data(parts[3])
                    if len(parts) > 4 and parts[4]:
                        cinema['screen_areas'] = parse_multiline_data(parts[4])
                    if len(parts) > 5 and parts[5]:
                        cinema['seat_counts'] = parse_multiline_data(parts[5])
                    
                    # 检查后续行是否有多行数据的续行
                    k = i + 1
                    while k < len(data_lines):
                        next_line = data_lines[k]
                        if not next_line:
                            k += 1
                            continue
                        
                        # 如果下一行是新的影院记录，停止
                        next_parts = []
                        next_current_part = ""
                        next_in_quotes = False
                        
                        for char in next_line:
                            if char == '"':
                                next_in_quotes = not next_in_quotes
                            elif char == ',' and not next_in_quotes:
                                next_parts.append(next_current_part)
                                next_current_part = ""
                            else:
                                next_current_part += char
                        next_parts.append(next_current_part)
                        
                        # 如果是新的影院记录，停止
                        if (len(next_parts) >= 2 and next_parts[0].strip() and 
                            next_parts[1].strip() and 'IMAX' in next_parts[1]):
                            break
                        
                        # 否则解析续行数据
                        if len(next_parts) > 0 and next_parts[0]:
                            cinema['screen_widths'].extend(parse_multiline_data(next_parts[0]))
                        if len(next_parts) > 1 and next_parts[1]:
                            cinema['screen_heights'].extend(parse_multiline_data(next_parts[1]))
                        if len(next_parts) > 2 and next_parts[2]:
                            cinema['screen_areas'].extend(parse_multiline_data(next_parts[2]))
                        if len(next_parts) > 3 and next_parts[3]:
                            cinema['seat_counts'].extend(parse_multiline_data(next_parts[3]))
                        
                        k += 1
                    
                    # 处理并添加影院
                    processed_cinema = process_cinema_data(cinema)
                    cinemas.append(processed_cinema)
                    
                    i = k
                else:
                    i += 1
        
        print(f"成功解析 {len(cinemas)} 家影院")
        
        # 显示前5个示例
        print("\n前5个影院示例:")
        for idx, cinema in enumerate(cinemas[:5], 1):
            print(f"{idx}. {cinema['name']}")
            print(f"   类型: {cinema['projectorType']}")
            print(f"   银幕: {cinema['screenWidth']}m × {cinema['screenHeight']}m ({cinema['screenArea']}㎡)")
            print(f"   座位: {cinema['seatCount']}")
            print(f"   音响: {cinema['audioSystem'] or '无'}")
            print()
        
        # 保存为JSON
        with open('../数字IMAX.json', 'w', encoding='utf-8') as f:
            json.dump(cinemas, f, ensure_ascii=False, indent=2)
        
        print("✅ 成功保存到 数字IMAX.json")
        
        # 生成统计信息
        stats = {
            'total': len(cinemas),
            'with_12_channel': len([c for c in cinemas if '12声道' in c['audioSystem']]),
            'without_screen_data': len([c for c in cinemas if c['screenArea'] == '--']),
            'without_seat_data': len([c for c in cinemas if c['seatCount'] == '--'])
        }
        
        print("\n📊 统计信息:")
        print(f"总数: {stats['total']}")
        print(f"12声道音响: {stats['with_12_channel']}")
        print(f"缺少银幕数据: {stats['without_screen_data']}")
        print(f"缺少座位数据: {stats['without_seat_data']}")
        
        print("\n🎉 转换完成！")
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
