const fs = require('fs');

console.log('开始转换 CSV 到 JSON...');

try {
    // 读取 UTF-8 编码的 CSV 文件
    const csvContent = fs.readFileSync('imax_cinemas_utf8.csv', 'utf8');
    
    // 分割行
    const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    console.log(`总行数: ${lines.length}`);
    
    // 解析表头
    const headers = lines[0].split(',').map(h => h.trim());
    console.log('表头:', headers);
    
    // 解析数据
    const cinemas = [];
    
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i];
        const values = [];
        let current = '';
        let inQuotes = false;
        
        // 简单的 CSV 解析，处理逗号分隔
        for (let j = 0; j < line.length; j++) {
            const char = line[j];
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        values.push(current.trim());
        
        // 确保有足够的列
        while (values.length < headers.length) {
            values.push('');
        }
        
        // 创建影院对象
        const cinema = {
            name: values[0] || '',
            projectorType: values[1] || '',
            screenArea: values[3] === 'N/A' ? '--' : values[3],
            screenWidth: values[4] === 'N/A' ? '--' : values[4],
            screenHeight: values[5] === 'N/A' ? '--' : values[5],
            seatCount: values[6] === 'N/A' ? '--' : values[6],
            openDate: '--', // CSV中没有开业时间，设为默认值
            remarks: values[2] === '√' ? '12声道音响系统' : ''
        };
        
        cinemas.push(cinema);
    }
    
    console.log(`成功解析 ${cinemas.length} 家影院`);
    
    // 显示前3个示例
    console.log('\n前3个影院示例:');
    cinemas.slice(0, 3).forEach((cinema, index) => {
        console.log(`${index + 1}. ${cinema.name}`);
        console.log(`   类型: ${cinema.projectorType}`);
        console.log(`   银幕: ${cinema.screenWidth}m × ${cinema.screenHeight}m (${cinema.screenArea}㎡)`);
        console.log(`   座位: ${cinema.seatCount}`);
        console.log(`   备注: ${cinema.remarks || '无'}`);
        console.log('');
    });
    
    // 保存为 JSON
    const jsonContent = JSON.stringify(cinemas, null, 2);
    fs.writeFileSync('imax_cinemas.json', jsonContent, 'utf8');
    
    console.log('✅ 成功保存到 imax_cinemas.json');
    
    // 生成统计信息
    const stats = {
        total: cinemas.length,
        commercial: cinemas.filter(c => c.projectorType.includes('Commercial')).length,
        laserXT: cinemas.filter(c => c.projectorType.includes('Laser XT')).length,
        gt3d: cinemas.filter(c => c.projectorType.includes('GT')).length,
        with12Channel: cinemas.filter(c => c.remarks.includes('12声道')).length
    };
    
    console.log('\n📊 统计信息:');
    console.log(`总数: ${stats.total}`);
    console.log(`Commercial Laser: ${stats.commercial}`);
    console.log(`Laser XT: ${stats.laserXT}`);
    console.log(`GT 3D Laser: ${stats.gt3d}`);
    console.log(`12声道音响: ${stats.with12Channel}`);
    
    console.log('\n🎉 转换完成！JSON 文件已准备好供 HTML 页面使用。');
    
} catch (error) {
    console.error('❌ 转换失败:', error.message);
    console.error(error.stack);
}
